#!/usr/bin/env python3
"""
Debug the combination of conditions for Pattern 2 (Asian Gap)
"""

import sys
import pandas as pd
sys.path.append('src')

def debug_condition_combination():
    """Debug why the combination of all 3 conditions results in 0 trades"""
    print("🔧 Debugging Pattern 2 Condition Combination...")
    
    # Load and prepare data (same as system)
    data_path = "data/2025.6.23FTSE_M1_UTCPlus01-M1-No Session.csv"
    df = pd.read_csv(data_path)
    df['DateTime'] = pd.to_datetime(df['DateTime'])
    df.set_index('DateTime', inplace=True)
    
    # Resample to 30min (Pattern 2 uses 30min timeframe)
    df_30min = df.resample('30min').agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min', 
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    print(f"📊 Total 30min bars: {len(df_30min)}")
    
    # Add required columns
    df_30min['hour'] = df_30min.index.hour
    df_30min['session_candle_number'] = df_30min.groupby(df_30min.index.date).cumcount() + 1
    
    # Condition 1: Asian session filter
    asian_mask = (df_30min['hour'] >= 22) | (df_30min['hour'] < 8)
    asian_bars = df_30min[asian_mask]
    print(f"✅ Condition 1 (Asian session): {len(asian_bars)} bars ({len(asian_bars)/len(df_30min)*100:.1f}%)")
    
    # Condition 2: candles_since_session_start <= 15
    # This is the CRITICAL condition - let's see what session_candle_number looks like
    candle_15_mask = df_30min['session_candle_number'] <= 15
    candle_15_bars = df_30min[candle_15_mask]
    print(f"✅ Condition 2 (≤15 candles since session start): {len(candle_15_bars)} bars ({len(candle_15_bars)/len(df_30min)*100:.1f}%)")
    
    # Combination of conditions 1 + 2
    asian_and_early = df_30min[asian_mask & candle_15_mask]
    print(f"✅ Conditions 1+2 (Asian + ≤15 candles): {len(asian_and_early)} bars ({len(asian_and_early)/len(df_30min)*100:.1f}%)")
    
    # Let's examine the session_candle_number in Asian session specifically
    if len(asian_bars) > 0:
        print(f"\n🔍 Asian Session Analysis:")
        print(f"   📊 Session candle numbers in Asian session:")
        asian_candle_dist = asian_bars['session_candle_number'].value_counts().sort_index()
        for candle_num in sorted(asian_candle_dist.index)[:20]:  # Show first 20
            count = asian_candle_dist[candle_num]
            print(f"      Candle {candle_num}: {count} bars")
            
        print(f"   📊 Asian bars with candle_number ≤ 15: {len(asian_bars[asian_bars['session_candle_number'] <= 15])}")
        
        # Check what hours these early Asian candles are in
        early_asian = asian_bars[asian_bars['session_candle_number'] <= 15]
        if len(early_asian) > 0:
            hour_dist = early_asian['hour'].value_counts().sort_index()
            print(f"   🕐 Hours for early Asian candles (≤15):")
            for hour, count in hour_dist.items():
                print(f"      Hour {hour:02d}: {count} bars")
    
    # Condition 3: ORB breakout above (6-candle period)
    # Calculate 6-candle ORB high
    df_30min['orb_high_6'] = df_30min['High'].rolling(window=6).max().shift(1)
    orb_breakout_mask = df_30min['High'] > df_30min['orb_high_6']
    orb_breakout_bars = df_30min[orb_breakout_mask]
    print(f"\n✅ Condition 3 (ORB breakout above): {len(orb_breakout_bars)} bars ({len(orb_breakout_bars)/len(df_30min)*100:.1f}%)")
    
    # Final combination: All 3 conditions
    all_conditions = df_30min[asian_mask & candle_15_mask & orb_breakout_mask]
    print(f"\n🎯 ALL CONDITIONS COMBINED: {len(all_conditions)} bars")
    
    if len(all_conditions) == 0:
        print("❌ ZERO TRADES EXPLAINED: No bars satisfy all 3 conditions simultaneously")
        
        # Let's see what's missing
        print("\n🔍 Debugging what's missing:")
        
        # Asian + ORB (without candle limit)
        asian_orb = df_30min[asian_mask & orb_breakout_mask]
        print(f"   Asian + ORB breakout: {len(asian_orb)} bars")
        
        # Early candles + ORB (without Asian filter)
        early_orb = df_30min[candle_15_mask & orb_breakout_mask]
        print(f"   Early candles + ORB breakout: {len(early_orb)} bars")
        
        # The problem is likely that Asian session starts at 22:00, but by then
        # we're already past candle 15 of the day (which started at 00:00)
        
        print("\n💡 HYPOTHESIS: Session candle counting issue")
        print("   - Asian session starts at 22:00")
        print("   - But session_candle_number counts from 00:00 (daily reset)")
        print("   - By 22:00, we're already at candle ~44 of the day")
        print("   - So Asian session bars are NEVER ≤ 15 candles since session start")
        
        # Verify this hypothesis
        asian_sample = asian_bars.head(10)
        print(f"\n📋 Sample Asian session bars:")
        for idx, row in asian_sample.iterrows():
            print(f"   {idx} (Hour {row['hour']:02d}): Candle #{row['session_candle_number']}")
    
    else:
        print(f"✅ Found {len(all_conditions)} potential trade opportunities")
        print(all_conditions[['hour', 'session_candle_number', 'High', 'orb_high_6']].head())

def main():
    debug_condition_combination()

if __name__ == "__main__":
    main()
