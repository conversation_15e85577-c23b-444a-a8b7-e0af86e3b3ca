#!/usr/bin/env python3
"""
Verify that the session filter fix resolves Pattern 2's zero trades issue
"""

import sys
import pandas as pd
sys.path.append('src')

def verify_session_fix():
    """Verify the corrected session times work for Pattern 2"""
    print("🔧 Verifying Session Filter Fix for Pattern 2...")
    
    # Load data
    data_path = "data/2025.6.23FTSE_M1_UTCPlus01-M1-No Session.csv"
    df = pd.read_csv(data_path)
    df['DateTime'] = pd.to_datetime(df['DateTime'])
    df.set_index('DateTime', inplace=True)
    
    # Resample to 30min (Pattern 2 timeframe)
    df_30min = df.resample('30min').agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min', 
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    df_30min['hour'] = df_30min.index.hour
    
    print(f"📊 Total 30min bars: {len(df_30min)}")
    print(f"📅 Data timezone: UTC+1 (as per filename)")
    
    # Test OLD session logic (GMT/UTC+0 based)
    old_asian_mask = (df_30min['hour'] >= 22) | (df_30min['hour'] < 8)
    old_asian_bars = df_30min[old_asian_mask]
    
    # Test NEW session logic (UTC+1 based) 
    new_asian_mask = (df_30min['hour'] >= 23) | (df_30min['hour'] < 9)
    new_asian_bars = df_30min[new_asian_mask]
    
    print(f"\n🌏 Asian Session Comparison:")
    print(f"   OLD logic (22:00-08:00): {len(old_asian_bars)} bars")
    print(f"   NEW logic (23:00-09:00): {len(new_asian_bars)} bars")
    
    # Show hour distribution for both
    print(f"\n🕐 Hour distribution comparison:")
    print(f"   OLD Asian hours: {sorted(old_asian_bars['hour'].unique())}")
    print(f"   NEW Asian hours: {sorted(new_asian_bars['hour'].unique())}")
    
    # Test Pattern 2 conditions with NEW session logic
    df_30min['session_candle_number'] = df_30min.groupby(df_30min.index.date).cumcount() + 1
    df_30min['orb_high_6'] = df_30min['High'].rolling(window=6).max().shift(1)

    # Pattern 2 conditions with NEW session logic - fix the filtering
    new_asian_early = df_30min[new_asian_mask & (df_30min['session_candle_number'] <= 15)]
    orb_breakout_mask = df_30min['High'] > df_30min['orb_high_6']
    
    # All conditions combined with NEW logic
    all_conditions_new = df_30min[
        new_asian_mask & 
        (df_30min['session_candle_number'] <= 15) & 
        orb_breakout_mask
    ]
    
    print(f"\n🎯 Pattern 2 Results with NEW session logic:")
    print(f"   Asian session bars: {len(new_asian_bars)}")
    print(f"   Asian + early candles (≤15): {len(new_asian_early)}")
    print(f"   ORB breakouts: {orb_breakout_mask.sum()}")
    print(f"   ALL CONDITIONS: {len(all_conditions_new)} potential trades")
    
    if len(all_conditions_new) > 0:
        print(f"   ✅ SUCCESS! Pattern 2 should now generate {len(all_conditions_new)} trades")
        print(f"   🔧 The session timezone fix resolved the zero trades issue")
        
        # Show sample trades
        print(f"\n📋 Sample qualifying bars:")
        sample = all_conditions_new.head(5)[['hour', 'session_candle_number', 'High', 'orb_high_6']]
        for idx, row in sample.iterrows():
            print(f"   {idx} (Hour {row['hour']:02d}): Candle #{row['session_candle_number']}, High={row['High']:.1f}")
    else:
        print(f"   ❌ Still 0 trades - other issues may remain")
    
    # Test the actual session filter function
    print(f"\n🧪 Testing session filter function:")
    from backtesting_rule_parser import BacktestingRuleParser
    parser = BacktestingRuleParser()
    
    # Test a few sample hours
    test_hours = [0, 8, 9, 15, 17, 22, 23]
    for hour in test_hours:
        # Create mock data
        mock_data = pd.DataFrame({'hour': [hour]}, index=[pd.Timestamp('2024-01-01 00:00:00')])
        mock_data.index = mock_data.index + pd.Timedelta(hours=hour)
        
        result = parser._session_filter(mock_data, 0, {'value': 'asian'})
        session_name = "Asian" if result else "Non-Asian"
        print(f"   Hour {hour:02d}: {session_name}")

def main():
    verify_session_fix()

if __name__ == "__main__":
    main()
