#!/usr/bin/env python3
"""
Debug trade execution issues for Pattern 2 (Asian Gap)
"""

import sys
import pandas as pd
sys.path.append('src')

def debug_trade_execution():
    """Debug why valid signals don't result in executed trades"""
    print("🔧 Debugging Trade Execution for Pattern 2...")
    
    # Load data
    data_path = "data/2025.6.23FTSE_M1_UTCPlus01-M1-No Session.csv"
    df = pd.read_csv(data_path)
    df['DateTime'] = pd.to_datetime(df['DateTime'])
    df.set_index('DateTime', inplace=True)
    
    # Resample to 30min
    df_30min = df.resample('30min').agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min', 
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    print(f"📊 30min data: {len(df_30min)} bars")
    
    # Test the actual pattern execution using the backtesting framework
    from backtesting.backtesting import Backtest, Strategy
    from config import config
    
    # Create a test strategy that mimics Pattern 2
    class Pattern2TestStrategy(Strategy):
        def init(self):
            self.signals = 0
            self.attempted_trades = 0
            self.successful_trades = 0
            
        def next(self):
            current_idx = len(self.data.Close) - 1
            if current_idx < 10:  # Need some history
                return
                
            # Recreate Pattern 2 conditions
            current_time = self.data.index[current_idx] if hasattr(self.data, 'index') else None
            
            # Simple session filter (Asian hours)
            if current_time:
                hour = current_time.hour
                is_asian = (hour >= 22) or (hour < 8)
            else:
                is_asian = True  # Fallback
                
            # Simple candle count (assume early in session)
            is_early = current_idx <= 15  # Simplified
            
            # ORB breakout (6-candle high)
            if current_idx >= 6:
                orb_high = max(self.data.High[current_idx-6:current_idx])
                is_breakout = self.data.High[current_idx] > orb_high
            else:
                is_breakout = False
                
            # Check if all conditions met
            if is_asian and is_early and is_breakout:
                self.signals += 1
                
                # Calculate trade parameters
                entry_price = self.data.Close[current_idx]
                stop_loss = entry_price * 0.99  # 1% stop loss
                take_profit = entry_price * 1.02  # 2% take profit
                position_size = config.DEFAULT_POSITION_SIZE_PCT / 100  # 1%
                
                print(f"📈 Signal #{self.signals} at bar {current_idx}")
                print(f"   Entry: {entry_price:.2f}, SL: {stop_loss:.2f}, TP: {take_profit:.2f}")
                print(f"   Position size: {position_size:.4f}")
                print(f"   Equity: {self.equity:.2f}")
                
                # Attempt to place trade
                try:
                    self.attempted_trades += 1
                    order = self.buy(size=position_size, sl=stop_loss, tp=take_profit)
                    if order:
                        self.successful_trades += 1
                        print(f"   ✅ Trade placed successfully")
                    else:
                        print(f"   ❌ Trade placement failed (order returned None)")
                except Exception as e:
                    print(f"   ❌ Trade placement failed with exception: {e}")
    
    # Run backtest
    print("\n🚀 Running Pattern 2 Test Backtest...")
    
    bt = Backtest(
        df_30min,
        Pattern2TestStrategy,
        cash=config.VALIDATION_INITIAL_CASH,
        commission=config.VALIDATION_COMMISSION,
        margin=1/config.DEFAULT_LEVERAGE,  # 1% margin for 100:1 leverage
        exclusive_orders=True
    )
    
    try:
        result = bt.run()
        
        print(f"\n📊 Backtest Results:")
        print(f"   💰 Final equity: ${result['Equity Final [$]']:,.2f}")
        print(f"   📈 Return: {result['Return [%]']:.2f}%")
        print(f"   🔢 Total trades: {result['# Trades']}")
        print(f"   📊 Win rate: {result['Win Rate [%]']:.1f}%")
        
        # Access strategy instance to get our custom metrics
        strategy_instance = bt._strategy
        print(f"\n🔍 Custom Metrics:")
        print(f"   📡 Signals generated: {strategy_instance.signals}")
        print(f"   🎯 Trades attempted: {strategy_instance.attempted_trades}")
        print(f"   ✅ Trades successful: {strategy_instance.successful_trades}")
        
        if strategy_instance.signals > 0 and result['# Trades'] == 0:
            print(f"\n❌ ISSUE IDENTIFIED: {strategy_instance.signals} signals generated but 0 trades executed!")
            print("   This confirms there's a trade execution problem, not a signal generation problem.")
            
    except Exception as e:
        print(f"❌ Backtest failed: {e}")

def debug_margin_calculation():
    """Debug margin calculation specifically"""
    print("\n🔧 Debugging Margin Calculation...")
    
    from config import config
    
    # Test margin calculation with typical values
    initial_cash = config.VALIDATION_INITIAL_CASH  # $100,000
    leverage = config.DEFAULT_LEVERAGE  # 100:1
    position_size_pct = config.DEFAULT_POSITION_SIZE_PCT / 100  # 1% = 0.01
    
    print(f"💰 Initial cash: ${initial_cash:,.2f}")
    print(f"🔢 Leverage: {leverage}:1")
    print(f"📊 Position size: {position_size_pct:.2%}")
    
    # Simulate FTSE price around 8300
    entry_price = 8300.0
    
    # Calculate position value
    position_value = initial_cash * position_size_pct
    print(f"💵 Position value: ${position_value:,.2f}")
    
    # Calculate required margin
    required_margin = position_value / leverage
    print(f"🏦 Required margin: ${required_margin:,.2f}")
    
    # Calculate margin utilization
    margin_utilization = required_margin / initial_cash * 100
    print(f"📈 Margin utilization: {margin_utilization:.2f}%")
    
    if margin_utilization > 100:
        print("❌ MARGIN ISSUE: Required margin exceeds available cash!")
    else:
        print("✅ Margin calculation looks OK")

def main():
    debug_margin_calculation()
    debug_trade_execution()

if __name__ == "__main__":
    main()
