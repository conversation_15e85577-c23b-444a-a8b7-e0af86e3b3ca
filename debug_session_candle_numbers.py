#!/usr/bin/env python3
"""
Debug session candle number calculation issue
"""

import sys
import pandas as pd
sys.path.append('src')

def debug_session_candle_calculation():
    """Debug the difference between daily reset vs session reset"""
    print("🔧 Debugging Session Candle Number Calculation...")
    
    # Load data
    data_path = "data/2025.6.23FTSE_M1_UTCPlus01-M1-No Session.csv"
    df = pd.read_csv(data_path)
    df['DateTime'] = pd.to_datetime(df['DateTime'])
    df.set_index('DateTime', inplace=True)
    
    # Resample to 30min
    df_30min = df.resample('30min').agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min', 
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    df_30min['hour'] = df_30min.index.hour
    
    print(f"📊 Total 30min bars: {len(df_30min)}")
    
    # Method 1: Daily reset (what my diagnostic used)
    df_30min['daily_candle_number'] = df_30min.groupby(df_30min.index.date).cumcount() + 1
    
    # Method 2: London session reset (what the system should use)
    from behavioral_intelligence import calculate_session_candle_numbers
    df_30min['session_candle_number'] = calculate_session_candle_numbers(df_30min)
    
    # Filter Asian session bars
    asian_mask = (df_30min['hour'] >= 22) | (df_30min['hour'] < 8)
    asian_bars = df_30min[asian_mask]
    
    print(f"\n🌏 Asian Session Analysis:")
    print(f"   Total Asian bars: {len(asian_bars)}")
    
    # Compare the two methods for Asian session
    print(f"\n📊 Daily Reset Method (midnight reset):")
    daily_early = asian_bars[asian_bars['daily_candle_number'] <= 15]
    print(f"   Asian bars with daily_candle_number ≤ 15: {len(daily_early)}")
    
    print(f"\n📊 Session Reset Method (London 08:00 reset):")
    session_early = asian_bars[asian_bars['session_candle_number'] <= 15]
    print(f"   Asian bars with session_candle_number ≤ 15: {len(session_early)}")
    
    # Show sample data to understand the difference
    print(f"\n📋 Sample Asian bars comparison:")
    sample = asian_bars.head(20)[['hour', 'daily_candle_number', 'session_candle_number']]
    for idx, row in sample.iterrows():
        print(f"   {idx} (Hour {row['hour']:02d}): Daily#{row['daily_candle_number']:2d} vs Session#{row['session_candle_number']:2d}")
    
    # Check specific hours
    print(f"\n🕐 Asian session candle numbers by hour (Session Reset Method):")
    for hour in sorted(asian_bars['hour'].unique()):
        hour_data = asian_bars[asian_bars['hour'] == hour]
        early_count = len(hour_data[hour_data['session_candle_number'] <= 15])
        total_count = len(hour_data)
        print(f"   Hour {hour:02d}: {early_count}/{total_count} bars ≤ 15 candles")
    
    # The key insight: Asian session spans the London reset boundary
    print(f"\n💡 KEY INSIGHT:")
    print(f"   Asian session: 22:00 → 08:00 (spans London session reset)")
    print(f"   London session reset: 08:00 (candle numbers reset to 1)")
    print(f"   Problem: Asian bars at 22:00-07:59 get high candle numbers")
    print(f"   Solution: Asian session needs its own reset logic at 22:00")

def test_asian_session_reset():
    """Test what happens if we reset candle numbers at Asian session start (22:00)"""
    print(f"\n🔧 Testing Asian Session Reset Logic...")
    
    # Load data
    data_path = "data/2025.6.23FTSE_M1_UTCPlus01-M1-No Session.csv"
    df = pd.read_csv(data_path)
    df['DateTime'] = pd.to_datetime(df['DateTime'])
    df.set_index('DateTime', inplace=True)
    
    # Resample to 30min
    df_30min = df.resample('30min').agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min', 
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    df_30min['hour'] = df_30min.index.hour
    
    # Create Asian session groups that reset at 22:00
    asian_session_groups = []
    current_session = 0
    
    for i, timestamp in enumerate(df_30min.index):
        if i == 0:
            asian_session_groups.append(current_session)
        else:
            prev_timestamp = df_30min.index[i-1]
            
            # New Asian session if we cross 22:00
            if (timestamp.date() != prev_timestamp.date() and timestamp.hour >= 22) or \
               (timestamp.date() == prev_timestamp.date() and 
                prev_timestamp.hour < 22 and timestamp.hour >= 22):
                current_session += 1
            
            asian_session_groups.append(current_session)
    
    # Calculate Asian session candle numbers
    asian_session_series = pd.Series(asian_session_groups, index=df_30min.index)
    df_30min['asian_candle_number'] = asian_session_series.groupby(asian_session_series).cumcount() + 1
    
    # Test with Asian session filter
    asian_mask = (df_30min['hour'] >= 22) | (df_30min['hour'] < 8)
    asian_bars = df_30min[asian_mask]
    
    # Check how many Asian bars are ≤ 15 with this method
    asian_early = asian_bars[asian_bars['asian_candle_number'] <= 15]
    print(f"   Asian bars with asian_candle_number ≤ 15: {len(asian_early)}")
    
    # Show sample
    print(f"\n📋 Sample with Asian session reset:")
    sample = asian_bars.head(20)[['hour', 'asian_candle_number']]
    for idx, row in sample.iterrows():
        print(f"   {idx} (Hour {row['hour']:02d}): Asian#{row['asian_candle_number']:2d}")
    
    # Now test the full Pattern 2 conditions with corrected candle numbers
    df_30min['orb_high_6'] = df_30min['High'].rolling(window=6).max().shift(1)
    orb_breakout_mask = df_30min['High'] > df_30min['orb_high_6']
    
    # All conditions with corrected Asian session candle numbers
    all_conditions = df_30min[
        asian_mask & 
        (df_30min['asian_candle_number'] <= 15) & 
        orb_breakout_mask
    ]
    
    print(f"\n🎯 Pattern 2 with CORRECTED Asian session candle numbers:")
    print(f"   Total qualifying bars: {len(all_conditions)}")
    
    if len(all_conditions) > 0:
        print(f"   ✅ SUCCESS: Found {len(all_conditions)} potential trades!")
        print(f"   This explains why Pattern 2 had 0 trades - session candle counting bug")
    else:
        print(f"   ❌ Still 0 trades - other issues remain")

def main():
    debug_session_candle_calculation()
    test_asian_session_reset()

if __name__ == "__main__":
    main()
