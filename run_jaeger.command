#!/bin/bash

# Jaeger LLM Pattern Discovery System - Main Launcher
# Double-click this file to run the complete pattern discovery process

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if terminal supports colors
check_color_support() {
    if [ -t 1 ] && [ "${TERM:-}" != "dumb" ] && command -v tput >/dev/null 2>&1 && tput colors >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Color definitions - Jaeger official colors (blue/cyan theme from Pacific Rim)
if check_color_support; then
    # Use standard ANSI colors for better compatibility
    JAEGER_BLUE='\033[38;5;39m'   # Bold blue - main Jaeger color
    JAEGER_CYAN='\033[1;36m'      # Bold cyan - accent color
    JAEGER_STEEL='\033[0;37m'     # Light gray - frame color
    JAEGER_WHITE='\033[1;37m'     # Bold white - highlights
    JAEGER_GOLD='\033[38;5;172m'  # Warmer, less bright golden color - energy/power color
    RESET='\033[0m'
    BOLD='\033[1m'
    DIM='\033[2m'
else
    # No color support - use empty strings
    JAEGER_BLUE=''
    JAEGER_CYAN=''
    JAEGER_STEEL=''
    JAEGER_WHITE=''
    JAEGER_GOLD=''
    RESET=''
    BOLD=''
    DIM=''
fi



# Function to display official JAEGER title
display_jaeger_title() {
    clear
    if check_color_support; then
        # Main JAEGER text (all gold)
        printf "${JAEGER_GOLD}${BOLD}      ██╗  █████╗  ███████╗  ██████╗  ███████╗ ██████╗ ${RESET}\n"
        printf "${JAEGER_GOLD}${BOLD}      ██║ ██╔══██╗ ██╔════╝ ██╔════╝  ██╔════╝ ██╔══██╗${RESET}\n"
        printf "${JAEGER_GOLD}${BOLD}      ██║ ███████║ █████╗   ██║  ███╗ █████╗   ██████╔╝${RESET}\n"
        printf "${JAEGER_GOLD}${BOLD} ██   ██║ ██╔══██║ ██╔══╝   ██║   ██║ ██╔══╝   ██╔══██╗${RESET}\n"
        printf "${JAEGER_GOLD}${BOLD} ╚█████╔╝ ██║  ██║ ███████╗ ╚██████╔╝ ███████╗ ██║  ██║${RESET}\n"
        printf "${JAEGER_GOLD}${BOLD}  ╚════╝  ╚═╝  ╚═╝ ╚══════╝  ╚═════╝  ╚══════╝ ╚═╝  ╚═╝${RESET}\n"

        printf "\n"
        printf "${JAEGER_GOLD}${BOLD}⚡ NEURAL HANDSHAKE PATTERN DISCOVERY SYSTEM ⚡${RESET}\n"
        printf "${JAEGER_CYAN}${DIM}「 DRIFT COMPATIBLE ENGAGED 」${RESET}\n"
        printf "\n"
        printf "${JAEGER_BLUE}🔧 AUTONOMOUS TRADING INTELLIGENCE${RESET}\n"
        printf "${JAEGER_CYAN}⚙️  ANALOG/DIGITAL HYBRID SYSTEM${RESET}\n"
        printf "\n"
        printf "${JAEGER_CYAN}${DIM} ┌─ INITIATING NEURAL HANDSHAKE SEQUENCE ─┐${RESET}\n"
        printf "${JAEGER_GOLD}${BOLD}│  Cortex Online • Drift channel stable  │${RESET}\n"
        printf "${JAEGER_CYAN}${DIM} └────────────────────────────────────────┘${RESET}\n"
        printf "\n"
    else
        printf "\n"
        printf "     ██╗ █████╗ ███████╗ ██████╗ ███████╗ ██████╗\n"
        printf "     ██║██╔══██╗██╔════╝██╔════╝ ██╔════╝██╔══██╗\n"
        printf "     ██║███████║█████╗  ██║  ███╗█████╗  ██████╔╝\n"
        printf "██   ██║██╔══██║██╔══╝  ██║   ██║██╔══╝  ██╔══██╗\n"
        printf "╚█████╔╝██║  ██║███████╗╚██████╔╝███████╗██║  ██║\n"
        printf " ╚════╝ ╚═╝  ╚═╝╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝\n"
        printf "\n"
        printf "⚡ NEURAL HANDSHAKE PATTERN DISCOVERY SYSTEM ⚡\n"
        printf "「 DRIFT COMPATIBLE ENGAGED 」\n"
        printf "\n"
        printf "🔧 AUTONOMOUS TRADING INTELLIGENCE\n"
        printf "⚙️  ANALOG/DIGITAL HYBRID SYSTEM\n"
        printf "\n"
        printf "┌─ INITIATING NEURAL HANDSHAKE SEQUENCE ─┐\n"
        printf "│  Cortex Online • Drift channel stable  │\n"
        printf "└────────────────────────────────────────┘\n"
        printf "\n"
    fi
}

# Function to show status message
show_status() {
    local message=$1
    if check_color_support; then
        printf "${JAEGER_CYAN}🔧 ${JAEGER_WHITE}%s${RESET}\n" "$message"
    else
        printf "🔧 %s\n" "$message"
    fi
}

# Function to start Pacific Rim music
start_pacific_rim_music() {
    # Check for Pacific Rim theme music files in order of preference
    MUSIC_FILES=(
        "$SCRIPT_DIR/branding/pacific_rim_theme.mp3"
        "$SCRIPT_DIR/branding/pacific_rim_theme.wav"
        "$SCRIPT_DIR/branding/pacific_rim_theme.m4a"
        "$SCRIPT_DIR/branding/jaeger_theme.mp3"
        "$SCRIPT_DIR/branding/jaeger_theme.wav"
        "$SCRIPT_DIR/branding/jaeger_theme.m4a"
    )

    MUSIC_FILE=""
    for file in "${MUSIC_FILES[@]}"; do
        if [ -f "$file" ]; then
            MUSIC_FILE="$file"
            break
        fi
    done

    if [ -z "$MUSIC_FILE" ]; then
        if check_color_support; then
            printf "${JAEGER_CYAN}${DIM}🎵 No Pacific Rim theme music found.${RESET}\n"
            printf "${JAEGER_CYAN}${DIM}   To add music, place one of these files in the /branding directory:${RESET}\n"
            printf "${JAEGER_CYAN}${DIM}   • branding/pacific_rim_theme.mp3/wav/m4a${RESET}\n"
            printf "${JAEGER_CYAN}${DIM}   • branding/jaeger_theme.mp3/wav/m4a${RESET}\n"
            printf "${JAEGER_CYAN}${DIM}   Continuing with epic silence...${RESET}\n"
        else
            printf "🎵 No Pacific Rim theme music found.\n"
            printf "   To add music, place one of these files in the /branding directory:\n"
            printf "   • branding/pacific_rim_theme.mp3/wav/m4a\n"
            printf "   • branding/jaeger_theme.mp3/wav/m4a\n"
            printf "   Continuing with epic silence...\n"
        fi

        # Play system sound as fallback (macOS)
        if command -v afplay >/dev/null 2>&1; then
            # Play a dramatic system sound in a loop
            (while true; do afplay /System/Library/Sounds/Sosumi.aiff; sleep 30; done) &
            MUSIC_PID=$!
            echo -e "${JAEGER_CYAN}${DIM}🎵 Epic system sounds engaged (PID: $MUSIC_PID)${RESET}"
        fi
        return 1
    fi

    # Start music in background (macOS) - play once only
    if command -v afplay >/dev/null 2>&1; then
        # Play the music file once
        afplay "$MUSIC_FILE" &
        MUSIC_PID=$!
        echo -e "${JAEGER_CYAN}🎵 Pacific Rim Theme Playing: $(basename "$MUSIC_FILE") (PID: $MUSIC_PID)${RESET}"
        return 0
    elif command -v mpg123 >/dev/null 2>&1; then
        mpg123 -q "$MUSIC_FILE" &
        MUSIC_PID=$!
        echo -e "${JAEGER_CYAN}🎵 Pacific Rim Theme Playing: $(basename "$MUSIC_FILE") (PID: $MUSIC_PID)${RESET}"
        return 0
    elif command -v vlc >/dev/null 2>&1; then
        vlc --intf dummy --play-and-exit "$MUSIC_FILE" &
        MUSIC_PID=$!
        echo -e "${JAEGER_CYAN}🎵 Pacific Rim Theme Playing: $(basename "$MUSIC_FILE") (PID: $MUSIC_PID)${RESET}"
        return 0
    else
        echo -e "${JAEGER_CYAN}${DIM}🎵 No audio player found, continuing silently...${RESET}"
        return 1
    fi
}

# Function to stop music
stop_music() {
    if [ ! -z "$MUSIC_PID" ]; then
        kill $MUSIC_PID 2>/dev/null
        echo -e "${JAEGER_CYAN}${DIM}🎵 Music stopped.${RESET}"
    fi
}

# Trap to ensure music stops on script exit
trap stop_music EXIT

# Display the official JAEGER title
display_jaeger_title

# Start Pacific Rim music
start_pacific_rim_music
sleep 2

echo -e "${JAEGER_GOLD}🚀 INITIATING JAEGER DEPLOYMENT SEQUENCE...${RESET}"
echo ""

# Step 1: Environment Check
show_status "Checking virtual environment..."

if [ ! -d "llm_env" ]; then
    echo -e "${JAEGER_GOLD}❌ Virtual environment not found. Running setup first...${RESET}"
    echo -e "${JAEGER_CYAN}🔧 Please run: ./bin/setup.sh${RESET}"
    echo ""
    read -p "Press Enter to exit..."
    stop_music
    exit 1
fi

echo -e "${JAEGER_CYAN}✅ Virtual environment verified${RESET}"

# Function to check if LM Studio is running
check_lm_studio() {
    LM_STUDIO_URL="http://localhost:1234"
    if curl -s ${LM_STUDIO_URL}/v1/models >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to start LM Studio (macOS)
start_lm_studio() {
    echo -e "${JAEGER_WHITE}🔍 Looking for LM Studio neural interface...${RESET}"

    # Common LM Studio installation paths
    LM_STUDIO_PATHS=(
        "/Applications/LM Studio.app"
        "$HOME/Applications/LM Studio.app"
        "/Applications/LMStudio.app"
        "$HOME/Applications/LMStudio.app"
    )

    for path in "${LM_STUDIO_PATHS[@]}"; do
        if [ -d "$path" ]; then
            echo -e "${JAEGER_CYAN}📱 Found LM Studio at: $path${RESET}"
            echo -e "${JAEGER_BLUE}🚀 Starting neural interface...${RESET}"
            open "$path"

            echo -e "${JAEGER_WHITE}⏳ Waiting for neural handshake initialization...${RESET}"
            for i in {1..30}; do
                if check_lm_studio; then
                    echo -e "${JAEGER_CYAN}✅ Neural interface online!${RESET}"
                    return 0
                fi
                echo -e "${JAEGER_STEEL}${DIM}   Establishing connection... ($i/30)${RESET}"
                sleep 2
            done

            echo -e "${JAEGER_GOLD}⚠️  Neural interface started but handshake incomplete${RESET}"
            echo -e "${JAEGER_CYAN}   Please manually load a model in LM Studio${RESET}"
            echo -e "${JAEGER_WHITE}   Recommended: Llama 3.1 8B Instruct${RESET}"
            echo ""
            read -p "Press Enter when model is loaded..."

            if check_lm_studio; then
                echo -e "${JAEGER_CYAN}✅ Neural handshake established!${RESET}"
                return 0
            else
                echo -e "${JAEGER_GOLD}❌ Neural handshake failed${RESET}"
                return 1
            fi
        fi
    done

    echo -e "${JAEGER_GOLD}❌ LM Studio not found in common locations${RESET}"
    echo -e "${JAEGER_CYAN}   Please install LM Studio from: https://lmstudio.ai/${RESET}"
    echo -e "${JAEGER_WHITE}   Or start it manually and load a model${RESET}"
    echo ""
    read -p "Press Enter when LM Studio is running with a model loaded..."

    if check_lm_studio; then
        echo -e "${JAEGER_CYAN}✅ Neural interface responding!${RESET}"
        return 0
    else
        echo -e "${JAEGER_GOLD}❌ Neural interface not responding${RESET}"
        return 1
    fi
}

# Step 2: LM Studio Check
show_status "Checking LM Studio status..."

if check_lm_studio; then
    echo -e "${JAEGER_CYAN}✅ LM Studio is already running!${RESET}"
else
    echo -e "${JAEGER_GOLD}⚠️  LM Studio not running. Attempting to start...${RESET}"
    if ! start_lm_studio; then
        echo -e "${JAEGER_GOLD}❌ Failed to start LM Studio. Exiting...${RESET}"
        read -p "Press Enter to exit..."
        stop_music
        exit 1
    fi
fi

# Step 3: Enhanced Model Verification with Automatic Loading Support
show_status "Checking available models..."

echo -e "${JAEGER_CYAN}🔧 ${JAEGER_WHITE}Testing LM Studio models for compatibility...${RESET}"

# Use our enhanced LM Studio client to find working models (respects config file mode setting)
# Run interactively to allow manual model selection
source llm_env/bin/activate && python3 src/ai_integration/lm_studio_client.py
MODEL_TEST_EXIT_CODE=$?

if [ $MODEL_TEST_EXIT_CODE -eq 0 ]; then
    echo -e "${JAEGER_CYAN}✅ Model verification successful${RESET}"
else
    echo -e "${JAEGER_GOLD}❌ FAIL HARD: Model verification failed${RESET}"
    echo -e "${JAEGER_CYAN}🚫 NO FALLBACKS ALLOWED - System requires working model${RESET}"
    echo -e "${JAEGER_WHITE}   Possible solutions:${RESET}"
    echo -e "${JAEGER_WHITE}   1. Wait for the selected model to fully load in LM Studio${RESET}"
    echo -e "${JAEGER_WHITE}   2. Try a different model that loads faster${RESET}"
    echo -e "${JAEGER_WHITE}   3. Increase system resources (RAM/GPU)${RESET}"
    echo -e "${JAEGER_WHITE}   4. Check LM Studio logs for loading issues${RESET}"
    echo ""
    echo -e "${JAEGER_GOLD}   System will NOT attempt automatic fallbacks${RESET}"
    echo ""
    stop_music
    exit 1
fi

# Step 4: Environment Activation
show_status "Activating neural handshake environment..."

source llm_env/bin/activate
echo -e "${JAEGER_CYAN}✅ Neural handshake environment activated${RESET}"

# Step 5: Data Loading & Analysis
show_status "Loading market data and initializing AI systems..."

echo ""
echo -e "${JAEGER_BLUE}🧠 INITIATING CORTEX PATTERN DISCOVERY...${RESET}"
echo -e "${JAEGER_STEEL}${DIM}════════════════════════════════════════${RESET}"
echo -e "${JAEGER_GOLD}⚡ Neural handshake established${RESET}"
echo -e "${JAEGER_BLUE}🎯 Scanning for Kaiju market patterns...${RESET}"
echo ""

# Step 6: Pattern Discovery Execution
show_status "Executing pattern discovery algorithms..."
echo -e "${JAEGER_CYAN}${DIM}🔄 Neural pattern analysis in progress... (this may take several minutes)${RESET}"
echo ""

# Run the main pattern discovery
python src/cortex.py
CORTEX_EXIT_CODE=$?

echo ""

if [ "${CORTEX_EXIT_CODE:-1}" -eq 0 ]; then
    echo -e "${JAEGER_CYAN}🎉 PATTERN DISCOVERY MISSION COMPLETE!${RESET}"
    echo -e "${JAEGER_STEEL}${DIM}═══════════════════════════════════════════${RESET}"
    echo ""
    echo -e "${JAEGER_BLUE}📁 Check the 'results' folder for generated files (organized by symbol):${RESET}"
    echo -e "${JAEGER_CYAN}   • [SYMBOL]_trading_system_[timestamp].md - Complete analysis for specific symbol${RESET}"
    echo -e "${JAEGER_CYAN}   • Gipsy_Danger_XXX.mq4 - MT4 EA with individual pattern toggles${RESET}"
    echo -e "${JAEGER_CYAN}   • [SYMBOL]_analyzed_data_[timestamp].csv - Processed data for specific symbol${RESET}"
    echo ""
    echo -e "${JAEGER_BLUE}🤖 Each run creates a Gipsy_Danger_XXX.mq4 EA with sequential numbering and pattern controls${RESET}"
    echo -e "${JAEGER_WHITE}🎛️ Pattern toggles: EnablePattern1, EnablePattern2, EnablePattern3, etc.${RESET}"
    echo ""
    echo -e "${JAEGER_STEEL}${BOLD}⚙ ═══════════════════════════════════════════════════════════════ ⚙${RESET}"
    echo -e "${JAEGER_CYAN}${BOLD}           JAEGER DEPLOYMENT SUCCESSFUL - DRIFT COMPATIBLE           ${RESET}"
    echo -e "${JAEGER_STEEL}${BOLD}⚙ ═══════════════════════════════════════════════════════════════ ⚙${RESET}"
else
    echo -e "${JAEGER_GOLD}❌ Pattern discovery encountered an error (Exit code: $CORTEX_EXIT_CODE)${RESET}"
    echo -e "${JAEGER_CYAN}   Check jaeger.log for details${RESET}"
fi

echo ""
stop_music
read -p "Press Enter to exit..."
