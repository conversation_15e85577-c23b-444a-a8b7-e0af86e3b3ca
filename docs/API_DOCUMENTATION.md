![<PERSON>ae<PERSON> Logo](../branding/jaeger-logo.png)

# 📚 API Documentation

**Complete API reference for the revolutionary Jaeger trading system (Self-Correcting AI Architecture)**

## 🛡️ **LLM VALIDATION & CORRECTION SYSTEM**

### **Intelligent LLM Response Validator**
Revolutionary automatic validation and correction system that eliminates 95%+ of LLM format failures.

#### **Basic Usage:**
```python
from llm_response_validator import LLMResponseValidator

# Initialize validator with retry configuration
validator = LLMResponseValidator(max_retries=2, retry_delay=1.0)

# Validate and correct LLM response automatically
corrected_response, patterns, was_corrected = validator.validate_and_correct(
    llm_client=your_llm_client,
    original_prompt=stage2_prompt,
    llm_response=raw_llm_response
)

# Check results
if was_corrected:
    print(f"Response corrected successfully: {len(patterns)} patterns")
else:
    print(f"Response valid on first attempt: {len(patterns)} patterns")
```

#### **Advanced Usage:**
```python
# Get validation statistics
stats = validator.get_statistics()
print(f"Success rate: {stats['overall_success_rate']:.1f}%")
print(f"Error categories: {stats['error_categories']}")

# Reset statistics for new session
validator.reset_statistics()

# Manual error categorization
error_category = validator._categorize_error(exception, response)
correction_prompt = validator._generate_correction_prompt(
    original_prompt, failed_response, error_category, attempt_number
)
```

#### **Error Categories Handled:**
- **not_json_format**: Response isn't JSON format
- **malformed_json**: Invalid JSON syntax
- **missing_pattern_name**: Required pattern_name field missing
- **missing_entry_conditions**: Required entry_conditions field missing
- **missing_exit_conditions**: Required exit_conditions field missing
- **invalid_condition_type**: Using unsupported condition types
- **no_patterns_found**: No valid patterns extracted
- **unknown_error**: Catch-all for unexpected issues

## 🚀 **TWO-STAGE PATTERN DISCOVERY APIS**

### **Two-Stage Pattern Discovery System**
Revolutionary two-stage pattern discovery APIs that combine Tom Hougaard's sophisticated methodology with backtesting compatibility.

### **1. Tom Hougaard Discovery Prompts (Stage 1)**

#### **Basic Usage:**
```python
from ai_integration.situational_prompts import TomHougaardDiscoveryPrompts

# Generate sophisticated discovery prompt
discovery_prompt = TomHougaardDiscoveryPrompts.generate_stage1_discovery_prompt(
    ohlc_data=market_data,
    market_summaries=behavioral_analysis,
    performance_feedback=learning_data
)

# Send to LLM for sophisticated pattern discovery
sophisticated_patterns = your_llm_client.send_prompt(discovery_prompt)
```

#### **Advanced Usage:**
```python
# Get Tom Hougaard core principles
principles = TomHougaardDiscoveryPrompts.get_tom_hougaard_core_principles()

# Get discovery methodology steps
methodology = TomHougaardDiscoveryPrompts.get_discovery_methodology_steps()

# Get pattern categories to explore
categories = TomHougaardDiscoveryPrompts.get_pattern_categories_to_explore()

# Get discovery examples for inspiration
examples = TomHougaardDiscoveryPrompts.get_discovery_examples()
```

### **2. Pattern Translation Prompts (Stage 2)**

#### **Basic Usage:**
```python
from ai_integration.pattern_translation_prompts import PatternTranslationPrompts

# Translate sophisticated patterns to backtesting format
translation_prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(
    sophisticated_patterns
)

# Send to LLM for translation
backtesting_patterns = your_llm_client.send_prompt(translation_prompt)

# Validate translation output
validation = PatternTranslationPrompts.validate_translation_output(backtesting_patterns)
```

#### **Advanced Usage:**
```python
# Get backtesting constraints
constraints = PatternTranslationPrompts.get_backtesting_constraints()

# Get translation principles
principles = PatternTranslationPrompts.get_translation_principles()

# Get translation examples
examples = PatternTranslationPrompts.get_translation_examples()
```

### **3. Enhanced Cortex Orchestration**

#### **Basic Usage:**
```python
from cortex import Cortex

# Initialize Cortex with two-stage discovery
cortex = Cortex()

# Run two-stage pattern discovery
results = cortex.discover_patterns(
    symbol='EURUSD',
    csv_file='data/EURUSD.csv'
)
```

### **4. Backtesting Rule Parser**

#### **Basic Usage:**
```python
from backtesting_rule_parser import parse_backtesting_rules

# Parse LLM response into backtesting functions
rule_functions = parse_backtesting_rules(llm_response)

# Test functions with OHLC data
for func in rule_functions:
    signal = func(ohlc_data, current_index)
    if signal:
        print(f"Signal: {signal}")
```

#### **Advanced Usage:**
```python
from backtesting_rule_parser import BacktestingRuleParser

# Create parser instance
parser = BacktestingRuleParser()

# Parse LLM response into rule objects
rules = parser.parse_llm_response(llm_response)

# Generate Python functions
functions = parser.generate_python_functions()

# Access individual rules
for rule in rules:
    print(f"Rule: {rule.name}")
    print(f"Entry: {rule.entry_logic}")
    print(f"Direction: {rule.direction}")
```

#### **Rule Object Structure:**
```python
@dataclass
class BacktestingTradingRule:
    rule_id: int
    name: str
    market_logic: str
    entry_logic: str
    direction: str  # 'long' or 'short'
    stop_logic: str
    target_logic: str
    position_size: float
    timeframe: str = "5min"
```

### **2. Walk-Forward Validator**

#### **Basic Usage:**
```python
from backtesting_walk_forward_validator import validate_backtesting_patterns

# Validate patterns with walk-forward analysis
validation_results = validate_backtesting_patterns(llm_response, ohlc_data)

if validation_results['success']:
    profitable_patterns = validation_results['profitable_patterns']
    success_rate = validation_results['success_rate']
    print(f"Found {len(profitable_patterns)} profitable patterns ({success_rate:.1f}% success)")
```

#### **Advanced Usage:**
```python
from backtesting_walk_forward_validator import BacktestingWalkForwardValidator

# Create validator with custom thresholds
validator = BacktestingWalkForwardValidator(
    min_return_threshold=5.0,
    min_consistency_score=60.0,
    min_win_rate=45.0,
    max_drawdown_threshold=15.0
)

# Validate patterns
validation_results = validator.validate_patterns(llm_response, ohlc_data)

# Generate validation report
report = validator.generate_validation_report(validation_results)
print(report)
```

#### **Validation Results Structure:**
```python
{
    'success': bool,
    'total_patterns': int,
    'profitable_patterns': List[BacktestingTradingRule],
    'validation_results': Dict[int, Dict],
    'success_rate': float
}
```

### **3. Hard-coded MT4 Converter**

#### **Basic Usage:**
```python
from hardcoded_mt4_converter import convert_profitable_patterns_to_mt4

# Convert validated patterns to MT4 EA
mt4_ea_code = convert_profitable_patterns_to_mt4(profitable_patterns, "My_EA")

# Save EA file
with open("My_EA.mq4", "w") as f:
    f.write(mt4_ea_code)
```

#### **Advanced Usage:**
```python
from hardcoded_mt4_converter import HardcodedMT4Converter

# Create converter instance
converter = HardcodedMT4Converter()

# Convert rules to MT4
mt4_code = converter.convert_rules_to_mt4(profitable_rules, "Advanced_EA")

# Check for conversion errors
if converter.conversion_errors:
    print("Conversion errors:", converter.conversion_errors)
```

### **4. Complete Two-Stage Workflow**

#### **Basic Usage:**
```python
from cortex import Cortex

# Use the complete two-stage discovery system
cortex = Cortex()
results = cortex.discover_patterns(
    symbol='EURUSD',
    csv_file='data/EURUSD.csv'
)
```

#### **Advanced Usage:**
```python
# Manual two-stage process
from ai_integration.situational_prompts import TomHougaardDiscoveryPrompts
from ai_integration.pattern_translation_prompts import PatternTranslationPrompts

# Stage 1: ORB Discovery
discovery_prompt = TomHougaardDiscoveryPrompts.generate_stage1_discovery_prompt(
    ohlc_data=data,
    market_summaries=orb_analysis,
    performance_feedback=learning_data
)

# Stage 2: Translation
sophisticated_patterns = llm_client.send_message(discovery_prompt)
translation_prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(
    sophisticated_patterns
)
backtesting_patterns = llm_client.send_message(translation_prompt)
```

## 🔄 **INTEGRATION WORKFLOW**

### **Complete Two-Stage Pipeline:**
```python
# 1. Stage 1: ORB-Focused Discovery
from ai_integration.situational_prompts import TomHougaardDiscoveryPrompts
discovery_prompt = TomHougaardDiscoveryPrompts.generate_stage1_discovery_prompt(ohlc_data, orb_summaries, feedback)
orb_patterns = llm_client.send_message(discovery_prompt)

# 2. Stage 2: ORB Translation
from ai_integration.pattern_translation_prompts import PatternTranslationPrompts
translation_prompt = PatternTranslationPrompts.generate_stage2_translation_prompt(orb_patterns)
backtesting_patterns = llm_client.send_message(translation_prompt)

# 3. Parse into backtesting functions
from backtesting_rule_parser import parse_backtesting_rules
rule_functions = parse_backtesting_rules(backtesting_patterns)

# 4. Validate with walk-forward analysis
from backtesting_walk_forward_validator import validate_backtesting_patterns
validation_results = validate_backtesting_patterns(backtesting_patterns, ohlc_data)

# 5. Convert profitable patterns to MT4
from hardcoded_mt4_converter import convert_profitable_patterns_to_mt4
if validation_results['success']:
    profitable_patterns = validation_results['profitable_patterns']
    mt4_ea_code = convert_profitable_patterns_to_mt4(profitable_patterns, "Validated_EA")

# 6. Generate files (existing file_generator.py integration)
from file_generator import FileGenerator
file_gen = FileGenerator()
generated_files = file_gen.generate_trading_system_files(
    cortex_results, backtest_results, validation_results
)
```

## 🛠️ **MIGRATION FROM V1.0**

### **For Existing Code:**
- **No changes required** - Cortex usage remains identical
- **Improved reliability** - Same interface, better results
- **Enhanced outputs** - Better MT4 EAs and validation

### **For Custom Integrations:**
- **Replace**: `llm_rule_parser.py` → `backtesting_rule_parser.py`
- **Add**: Walk-forward validation step
- **Update**: MT4 generation to use hard-coded converter
- **Simplify**: LLM prompts to backtesting-only format

## 🧪 **TESTING APIS**

### **Test Utilities:**
```python
# Test parsing reliability
from test_backtesting_only import test_backtesting_parser
success_rate = test_backtesting_parser()

# Test complete refactoring benefits
from test_refactoring_benefits import run_comprehensive_test
overall_success = run_comprehensive_test()

# Debug parser issues
from debug_backtesting_parser import run_debug_tests
debug_results = run_debug_tests()
```

## 📊 **PERFORMANCE MONITORING**

### **Success Metrics:**
```python
# Track parsing success rates
parsing_success = len(parsed_rules) / total_patterns * 100

# Monitor signal generation rates
signal_rate = signals_found / total_tests * 100

# Measure MT4 conversion reliability
mt4_reliability = successful_conversions / total_conversions * 100

# Validate walk-forward success
wf_success = profitable_patterns / total_patterns * 100
```

## 🎯 **BEST PRACTICES**

### **1. Pattern Validation:**
- Always use walk-forward validation before MT4 deployment
- Set appropriate profitability thresholds for your strategy
- Monitor pattern performance over time

### **2. Error Handling:**
- Check validation results before proceeding to MT4 conversion
- Handle parsing errors gracefully
- Log conversion issues for debugging

### **3. Performance Optimization:**
- Use appropriate walk-forward splits for your data size
- Adjust validation thresholds based on market conditions
- Monitor system performance metrics regularly

---

**📋 For complete examples and integration guides, see:**
- [REFACTORING_REPORT.md](../REFACTORING_REPORT.md)
- [TECHNICAL_DOCUMENTATION.md](TECHNICAL_DOCUMENTATION.md)
- [USER_DOCUMENTATION.md](USER_DOCUMENTATION.md)
