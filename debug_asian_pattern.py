#!/usr/bin/env python3
"""
Debug script to investigate why Pattern 2 (Asian Gap) generated 0 trades
"""

import sys
import os
import pandas as pd
import numpy as np
sys.path.append('src')

def debug_asian_session_filter():
    """Debug the Asian session filter specifically"""
    print("🔧 Debugging Asian Session Filter...")

    # Load the same data that was used in backtesting
    data_path = "data/2025.6.23FTSE_M1_UTCPlus01-M1-No Session.csv"
    print(f"📊 Loading data from: {data_path}")

    # Load data with correct column name
    df = pd.read_csv(data_path)
    df['DateTime'] = pd.to_datetime(df['DateTime'])
    df.set_index('DateTime', inplace=True)
    
    print(f"   ✅ Loaded {len(df)} rows")
    print(f"   📅 Date range: {df.index.min()} to {df.index.max()}")
    
    # Resample to 30min (same as Pattern 2 used)
    df_30min = df.resample('30min').agg({
        'Open': 'first',
        'High': 'max', 
        'Low': 'min',
        'Close': 'last',
        'Volume': 'sum'
    }).dropna()
    
    print(f"   📊 30min data: {len(df_30min)} bars")
    
    # Add hour column
    df_30min['hour'] = df_30min.index.hour
    
    # Test Asian session filter logic
    asian_hours = (df_30min['hour'] >= 22) | (df_30min['hour'] < 8)
    asian_bars = df_30min[asian_hours]
    
    print(f"   🌏 Asian session bars (22:00-08:00): {len(asian_bars)}")
    print(f"   📊 Asian session percentage: {len(asian_bars)/len(df_30min)*100:.1f}%")
    
    if len(asian_bars) > 0:
        print(f"   📅 First Asian bar: {asian_bars.index[0]}")
        print(f"   📅 Last Asian bar: {asian_bars.index[-1]}")
        
        # Show sample of Asian hours
        sample_hours = asian_bars['hour'].value_counts().sort_index()
        print(f"   🕐 Asian hours distribution:")
        for hour, count in sample_hours.items():
            print(f"      Hour {hour:02d}: {count} bars")
    else:
        print("   ❌ No Asian session bars found!")
    
    return len(asian_bars) > 0

def debug_pattern_2_conditions():
    """Debug all conditions for Pattern 2"""
    print("\n🔧 Debugging Pattern 2 Conditions...")
    
    # Pattern 2 conditions:
    # 1. orb_breakout_above with 6-candle opening range  
    # 2. candles_since_session_start value: 15
    # 3. session_filter value: "asian"
    
    from backtesting_rule_parser import BacktestingRuleParser
    
    # Create test pattern
    pattern_2 = {
        "pattern_name": "Asian Gap",
        "entry_conditions": [
            {
                "condition": "orb_breakout_above",
                "orb_period_minutes": 60,
                "orb_period_bars": 6
            },
            {
                "condition": "candles_since_session_start", 
                "value": 15
            },
            {
                "condition": "session_filter",
                "value": "asian"
            }
        ],
        "exit_conditions": [
            {
                "condition": "risk_reward_ratio",
                "risk": 1,
                "reward": 2
            }
        ]
    }
    
    parser = BacktestingRuleParser()
    
    try:
        pattern_obj = parser._create_pattern_from_json(pattern_2)
        print(f"   ✅ Pattern created: {pattern_obj.pattern_name}")
        print(f"   📋 Entry conditions: {len(pattern_obj.entry_conditions)}")
        
        # Test each condition individually
        for i, condition in enumerate(pattern_obj.entry_conditions):
            condition_type = condition['condition']
            print(f"   🔧 Condition {i+1}: {condition_type}")
            
            if condition_type in parser.supported_conditions:
                print(f"      ✅ Supported")
            else:
                print(f"      ❌ NOT supported")
                
        return True
        
    except Exception as e:
        print(f"   ❌ Pattern creation failed: {e}")
        return False

def debug_orb_data_availability():
    """Check if ORB data columns are available"""
    print("\n🔧 Debugging ORB Data Availability...")

    try:
        # Load data manually (same as system does)
        data_path = "data/2025.6.23FTSE_M1_UTCPlus01-M1-No Session.csv"
        df = pd.read_csv(data_path)
        df['DateTime'] = pd.to_datetime(df['DateTime'])
        df.set_index('DateTime', inplace=True)

        # Resample to 30min (same as Pattern 2)
        df_30min = df.resample('30min').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()

        print(f"   📊 30min data: {len(df_30min)} bars")
        print(f"   📋 Base columns: {list(df_30min.columns)}")

        # Check if we can simulate ORB breakouts
        # Simple ORB logic: compare current high to max of previous 6 bars
        df_30min['orb_high_6'] = df_30min['High'].rolling(window=6).max().shift(1)
        df_30min['orb_breakout_up'] = df_30min['High'] > df_30min['orb_high_6']

        breakouts = df_30min['orb_breakout_up'].sum()
        print(f"   🚀 Simulated ORB breakouts up: {breakouts}")

        # Add session candle numbers
        df_30min['hour'] = df_30min.index.hour
        df_30min['session_candle_number'] = df_30min.groupby(df_30min.index.date).cumcount() + 1

        print(f"   📊 Session candle numbers: {df_30min['session_candle_number'].max()} max")

        return True

    except Exception as e:
        print(f"   ❌ ORB data check failed: {e}")
        return False

def main():
    """Run all diagnostic tests"""
    print("🚀 Debugging Pattern 2 (Asian Gap) - Zero Trades Issue")
    print("=" * 60)
    
    tests = [
        debug_asian_session_filter,
        debug_pattern_2_conditions, 
        debug_orb_data_availability
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ❌ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print(f"🎯 Diagnostic Results: {sum(results)}/{len(results)} tests passed")
    
    if all(results):
        print("✅ All diagnostic tests passed - issue may be in condition combination")
    else:
        print("❌ Some diagnostic tests failed - root cause identified")

if __name__ == "__main__":
    main()
